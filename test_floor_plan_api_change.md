# Floor Plan API Endpoint Change Test Plan

## Summary of Changes Made

### Modified File: `RESClient\Services\Implementations\FloorPlanModuleGenerator.cs`

1. **API Endpoint Change** (Line 563):
   - Changed from: `_configService.GetDrawingProcessingApiUrl("process")`
   - Changed to: `_configService.GetDrawingProcessingApiUrl("generate-floor-plans")`

2. **Progress Message Updates**:
   - Line 500: "处理DWG文件" → "生成平面图"
   - Line 514: "处理DWG文件失败" → "生成平面图失败"
   - Line 574: "上传文件到API" → "上传文件到平面图生成API"
   - Line 634: "处理状态" → "平面图生成状态"
   - Line 645: "处理失败" → "平面图生成失败"
   - Line 883: "下载处理结果" → "下载平面图生成结果"

3. **Method Documentation Updates**:
   - Updated method comments to reflect floor plan generation instead of generic processing

## Testing Checklist

### Pre-Testing Requirements
- [ ] Ensure RESServer is running with administrator privileges
- [ ] Verify the new `/api/DrawingProcessing/generate-floor-plans` endpoint is available
- [ ] Confirm AutoCAD plugin with `RES_GENERATE_FLOOR_PLANS` command is loaded

### Test Cases

#### 1. Basic Functionality Test
- [ ] Select a DWG file with valid floor plan data
- [ ] Run the "房产分层测绘图" module generation
- [ ] Verify the API call goes to the new endpoint
- [ ] Confirm progress messages show "平面图生成" instead of "处理"
- [ ] Check that the generated output contains proper floor plans (not just split drawings)

#### 2. Error Handling Test
- [ ] Test with invalid DWG file
- [ ] Test with DWG file without floor plan data
- [ ] Verify error messages are updated appropriately
- [ ] Confirm graceful fallback behavior

#### 3. Progress Monitoring Test
- [ ] Monitor progress messages during generation
- [ ] Verify all status updates use new terminology
- [ ] Check that session management works correctly
- [ ] Confirm download functionality remains intact

#### 4. Output Comparison Test
- [ ] Compare output from old endpoint vs new endpoint
- [ ] Verify new endpoint produces floor plans with templates
- [ ] Check that file formats and structure are compatible
- [ ] Ensure Word document generation still works correctly

### Expected Behavior Changes

#### What Should Change:
- API endpoint URL from `/process` to `/generate-floor-plans`
- AutoCAD command from `RESSavePrintDrawings` to `RES_GENERATE_FLOOR_PLANS`
- Progress messages reflect floor plan generation
- Output should contain properly formatted floor plans with templates

#### What Should Remain the Same:
- Session management (status, files, download endpoints)
- Response format and data structure
- Error handling patterns
- File upload and download mechanisms
- Word document generation logic

## Rollback Plan

If issues are encountered, revert the following change:

```csharp
// Revert line 563 in FloorPlanModuleGenerator.cs
string apiUrl = _configService.GetDrawingProcessingApiUrl("process");
```

And update progress messages back to original terminology.

## Success Criteria

✅ **Test passes if:**
1. Floor plan generation completes successfully
2. Generated output contains proper floor plans (not just split drawings)
3. All progress messages use updated terminology
4. No breaking changes to existing functionality
5. Word document generation works correctly with new floor plan images

❌ **Test fails if:**
1. API calls fail or timeout
2. Generated output is empty or corrupted
3. Progress monitoring breaks
4. Word document generation fails
5. Any existing functionality is broken
