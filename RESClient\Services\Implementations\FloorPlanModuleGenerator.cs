using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Drawing;
using System.Drawing.Imaging;
using NPOI.XWPF.UserModel;
using System.Text.RegularExpressions;
using System.Net.Http;
using System.Text.Json;
using ICSharpCode.SharpZipLib.Zip;
using RESClient.Services;

namespace RESClient.Services.Implementations
{
    /// <summary>
    /// 房产分层测绘图模块生成器
    /// </summary>
    public class FloorPlanModuleGenerator : IReportModuleGenerator
    {
        private readonly RESServerConfigService _configService;

        /// <summary>
        /// 构造函数
        /// </summary>
        public FloorPlanModuleGenerator()
        {
            _configService = RESServerConfigService.Instance;
        }

        /// <summary>
        /// 模块名称
        /// </summary>
        public string ModuleName => "房产分层测绘图";

        /// <summary>
        /// 生成报告模块
        /// </summary>
        /// <param name="parameters">生成参数</param>
        /// <param name="progressCallback">进度回调</param>
        /// <returns>生成结果</returns>
        public async Task<bool> GenerateAsync(Dictionary<string, object> parameters, Action<int, string> progressCallback)
        {
            try
            {
                // 记录开始进度
                progressCallback?.Invoke(0, $"[INFO]开始生成{ModuleName}...");
                progressCallback?.Invoke(1, $"[INFO]{_configService.GetConfigurationSummary()}");

                // 1. 获取选择的数据路径
                if (!parameters.TryGetValue("DataFolder", out object dataDirectoryObj) || dataDirectoryObj == null)
                {
                    progressCallback?.Invoke(0, "[ERROR]未找到数据目录");
                    return false;
                }

                string dataDirectory = dataDirectoryObj.ToString();
                if (!Directory.Exists(dataDirectory))
                {
                    progressCallback?.Invoke(0, $"[ERROR]数据目录不存在: {dataDirectory}");
                    return false;
                }

                // 2. 获取输出目录
                if (!parameters.TryGetValue("OutputDir", out object outputDirectoryObj) || outputDirectoryObj == null)
                {
                    progressCallback?.Invoke(0, "[ERROR]未找到输出目录");
                    return false;
                }

                string outputDirectory = outputDirectoryObj.ToString();
                if (!Directory.Exists(outputDirectory))
                {
                    Directory.CreateDirectory(outputDirectory);
                }

                // 3. DWG文件处理管道
                progressCallback?.Invoke(5, "[INFO]检查DWG文件...");
                var dwgFiles = FindDwgFiles(dataDirectory);

                if (dwgFiles != null && dwgFiles.Length > 0)
                {
                    progressCallback?.Invoke(10, $"[INFO]找到{dwgFiles.Length}个DWG文件，开始处理...");

                    // 确保打印图文件夹存在并清理
                    string printImageFolder = Path.Combine(outputDirectory, "打印图");
                    if (Directory.Exists(printImageFolder))
                    {
                        progressCallback?.Invoke(12, "[INFO]清理现有打印图文件夹...");
                        try
                        {
                            Directory.Delete(printImageFolder, true);
                        }
                        catch (Exception ex)
                        {
                            progressCallback?.Invoke(0, $"[ERROR]清理打印图文件夹失败: {ex.Message}");
                            return false;
                        }
                    }

                    // 创建新的打印图文件夹
                    Directory.CreateDirectory(printImageFolder);
                    progressCallback?.Invoke(15, "[INFO]已创建打印图输出文件夹");

                    // 处理DWG文件
                    bool dwgProcessingResult = await ProcessDwgFiles(dwgFiles, printImageFolder, progressCallback);
                    if (!dwgProcessingResult)
                    {
                        progressCallback?.Invoke(0, "[ERROR]DWG文件处理失败");
                        return false;
                    }
                }
                else
                {
                    progressCallback?.Invoke(10, "[INFO]未找到DWG文件，跳过DWG处理步骤");
                }

                progressCallback?.Invoke(50, "[INFO]查找打印图文件...");

                // 4. 查找打印图文件
                var printImageFiles = FindPrintImageFiles(dataDirectory, outputDirectory);

                if (printImageFiles == null || printImageFiles.Length == 0)
                {
                    progressCallback?.Invoke(50, "[ERROR]未找到打印图文件");
                    return false;
                }

                progressCallback?.Invoke(60, $"[INFO]找到{printImageFiles.Length}个打印图文件");
                foreach (var file in printImageFiles)
                {
                    progressCallback?.Invoke(60, $"[DEBUG]找到文件: {Path.GetFileName(file)}");
                }

                // 5. 获取模板路径
                string templateDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "报告模板");
                string templatePath = Path.Combine(templateDirectory, "13_房产分层测绘图", "房产分层测绘图.docx");

                if (!File.Exists(templatePath))
                {
                    progressCallback?.Invoke(70, $"[ERROR]模板文件不存在: {templatePath}");
                    return false;
                }

                progressCallback?.Invoke(80, "[INFO]读取模板文件...");

                // 6. 创建输出文件路径
                string outputPath = Path.Combine(outputDirectory, $"{ModuleName}.docx");

                // 7. 处理图片并生成Word文档
                bool result = await Task.Run(() => GenerateWordDocument(templatePath, outputPath, printImageFiles, progressCallback));

                progressCallback?.Invoke(100, result ? $"[SUCCESS]{ModuleName}生成完成" : $"[ERROR]{ModuleName}生成失败");
                return result;
            }
            catch (Exception ex)
            {
                progressCallback?.Invoke(100, $"[ERROR]{ModuleName}生成失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 生成Word文档并插入图片
        /// </summary>
        /// <param name="templatePath">模板路径</param>
        /// <param name="outputPath">输出路径</param>
        /// <param name="imageFiles">图片文件路径数组</param>
        /// <param name="progressCallback">进度回调</param>
        /// <returns>是否成功</returns>
        private bool GenerateWordDocument(string templatePath, string outputPath, string[] imageFiles, Action<int, string> progressCallback)
        {
            // 创建临时文件
            string tempFilePath = Path.Combine(
                Path.GetTempPath(),
                $"temp_floorplan_{DateTime.Now.Ticks}.docx");
            
            try
            {
                // 复制模板到临时文件
                File.Copy(templatePath, tempFilePath, true);
                
                progressCallback?.Invoke(50, "[INFO]复制模板完成，准备处理图片...");

                // 打开文档
                XWPFDocument doc;
                using (FileStream fs = new FileStream(tempFilePath, FileMode.Open, FileAccess.ReadWrite))
                {
                    doc = new XWPFDocument(fs);
                }

                // 获取模板中的占位图片尺寸信息 (in EMUs)
                Tuple<long, long> firstPageImageEmuSize = null;
                Tuple<long, long> secondPageImageEmuSize = null;

                // 查找模板中的占位图片尺寸
                bool foundFirstPlaceholder = false;
                bool foundSecondPlaceholder = false;

                // 遍历段落和运行查找图片
                foreach (var element in doc.BodyElements.OfType<XWPFParagraph>())
                {
                    foreach (var run in element.Runs)
                    {
                        foreach (var picture in run.GetEmbeddedPictures())
                        {
                            var ctPicture = picture.GetCTPicture();
                            if (ctPicture?.spPr?.xfrm?.ext != null)
                            {
                                long widthEmu = ctPicture.spPr.xfrm.ext.cx;
                                long heightEmu = ctPicture.spPr.xfrm.ext.cy;

                                if (widthEmu > 0 && heightEmu > 0) // 确保尺寸有效
                                {
                                    if (!foundFirstPlaceholder)
                                    {
                                        firstPageImageEmuSize = Tuple.Create(widthEmu, heightEmu);
                                        foundFirstPlaceholder = true;
                                        progressCallback?.Invoke(55, $"[INFO]找到第一个占位图片，尺寸: {widthEmu}x{heightEmu} EMU");
                                    }
                                    else if (!foundSecondPlaceholder)
                                    {
                                        secondPageImageEmuSize = Tuple.Create(widthEmu, heightEmu);
                                        foundSecondPlaceholder = true;
                                        progressCallback?.Invoke(56, $"[INFO]找到第二个占位图片，尺寸: {widthEmu}x{heightEmu} EMU");
                                        break; // 找到两个就足够了
                                    }
                                }
                            }
                        }
                        if (foundSecondPlaceholder) break;
                    }
                    if (foundSecondPlaceholder) break;
                }
                
                // 如果第二个占位符未找到，使用第一个的尺寸
                if (foundFirstPlaceholder && !foundSecondPlaceholder)
                {
                    secondPageImageEmuSize = firstPageImageEmuSize;
                    foundSecondPlaceholder = true;
                }

                if (!foundFirstPlaceholder)
                {
                    progressCallback?.Invoke(60, "[ERROR]无法在模板中找到有效的占位图片尺寸");
                    return false;
                }

                // 存储所有包含图片的段落，以便后续替换
                var paragraphsWithImages = new List<XWPFParagraph>();
                foreach (var element in doc.BodyElements.OfType<XWPFParagraph>())
                {
                    foreach (var run in element.Runs)
                    {
                        if (run.GetEmbeddedPictures().Count > 0)
                        {
                            paragraphsWithImages.Add(element);
                            break;
                        }
                    }
                }

                progressCallback?.Invoke(65, "[INFO]删除模板中的占位图片...");

                // 只删除包含图片的段落，保留其他内容
                foreach (var paragraph in paragraphsWithImages)
                {
                    doc.RemoveBodyElement(doc.GetPosOfParagraph(paragraph));
                }

                // 如果只有一张图片，删除所有分页符及之后的内容
                if (imageFiles.Length == 1)
                {
                    progressCallback?.Invoke(70, "[INFO]只有一张图片，删除多余的页面...");
                    // 找到所有页面分隔符并删除它们以及它们之后的内容
                    List<int> pageBreakPositions = new List<int>();
                    for (int i = 0; i < doc.BodyElements.Count; i++)
                    {
                        var element = doc.BodyElements[i];
                        if (element is XWPFParagraph paragraph)
                        {
                            foreach (var run in paragraph.Runs)
                            {
                                if (run.GetCTR().GetBrList().Any(br => br.type == NPOI.OpenXmlFormats.Wordprocessing.ST_BrType.page))
                                {
                                    pageBreakPositions.Add(i);
                                    break;
                                }
                            }
                        }
                    }

                    // 从后向前删除页面分隔符和后续内容，避免索引变化的问题
                    pageBreakPositions.Reverse();
                    foreach (int pos in pageBreakPositions)
                    {
                        // 删除页面分隔符及其后的所有元素
                        for (int i = doc.BodyElements.Count - 1; i >= pos; i--)
                        {
                            doc.RemoveBodyElement(i);
                        }
                    }
                }

                progressCallback?.Invoke(75, "[INFO]添加图片到文档...");

                // 添加新图片
                for (int i = 0; i < imageFiles.Length; i++)
                {
                    string imagePath = imageFiles[i];
                    XWPFParagraph paragraph = doc.CreateParagraph();
                    paragraph.Alignment = ParagraphAlignment.CENTER; // 居中对齐
                    XWPFRun run = paragraph.CreateRun();

                    Tuple<long, long> targetEmuSize = (i == 0) ? firstPageImageEmuSize : secondPageImageEmuSize;

                    using (FileStream imageStream = new FileStream(imagePath, FileMode.Open, FileAccess.Read))
                    {
                        int pictureFormat = GetPictureTypeForNPOI(Path.GetExtension(imagePath));
                        
                        using (Image img = Image.FromStream(imageStream, false, false)) // 保持流打开，暂不验证图像数据
                        {
                            SizeF originalDpi = new SizeF(img.HorizontalResolution, img.VerticalResolution);
                            if (originalDpi.Width == 0) originalDpi.Width = 96f; // 默认DPI
                            if (originalDpi.Height == 0) originalDpi.Height = 96f;

                            Tuple<long, long> finalEmuDimensions = CalculateScaledEmuDimensions(
                                img.Size, 
                                originalDpi, 
                                targetEmuSize);

                            // 重置流位置以便AddPicture使用
                            imageStream.Position = 0;
                            run.AddPicture(imageStream, 
                                          pictureFormat, 
                                          Path.GetFileName(imagePath), 
                                          (int)finalEmuDimensions.Item1,
                                          (int)finalEmuDimensions.Item2);

                            progressCallback?.Invoke(80 + (i * 15 / imageFiles.Length), 
                                $"[INFO]添加图片 {i+1}/{imageFiles.Length}: {Path.GetFileName(imagePath)}");
                        }
                    }

                    // 只有当有多张图片时才添加分页符
                    if (imageFiles.Length > 1 && i < imageFiles.Length - 1)
                    {
                        // 每张图片后添加分页符，除了最后一张
                        doc.CreateParagraph().CreateRun().AddBreak(BreakType.PAGE);
                    }
                }

                progressCallback?.Invoke(95, "[INFO]保存文档...");

                // 保存填充后的文档到指定输出路径
                using (FileStream fs = new FileStream(outputPath, FileMode.Create, FileAccess.Write))
                {
                    doc.Write(fs);
                }

                return true;
            }
            catch (Exception ex)
            {
                progressCallback?.Invoke(90, $"[ERROR]生成文档失败: {ex.Message}");
                return false;
            }
            finally
            {
                // 删除临时文件
                try
                {
                    if (File.Exists(tempFilePath))
                    {
                        File.Delete(tempFilePath);
                    }
                }
                catch 
                { 
                    // 临时文件删除失败不影响主流程 
                }
            }
        }

        /// <summary>
        /// 计算按比例缩放的EMU尺寸，以使图像适合目标EMU尺寸
        /// </summary>
        /// <param name="originalImagePixelSize">原始图像的像素尺寸</param>
        /// <param name="originalImageDpi">原始图像的DPI</param>
        /// <param name="targetPlaceholderEmuSize">目标占位符EMU尺寸</param>
        /// <returns>包含缩放后宽度和高度（以EMU为单位）的Tuple</returns>
        private Tuple<long, long> CalculateScaledEmuDimensions(Size originalImagePixelSize, SizeF originalImageDpi, Tuple<long, long> targetPlaceholderEmuSize)
        {
            if (originalImagePixelSize.Width <= 0 || originalImagePixelSize.Height <= 0 ||
                originalImageDpi.Width <= 0 || originalImageDpi.Height <= 0 ||
                targetPlaceholderEmuSize.Item1 <= 0 || targetPlaceholderEmuSize.Item2 <= 0)
            {
                // 返回一个小的默认值或抛出异常，避免除零/零大小
                // 如果目标无效，则使用图像的自然大小，最大不超过合理的最大值
                // 或者，如果图像本身无效，则返回小尺寸
                if (targetPlaceholderEmuSize.Item1 <= 0 || targetPlaceholderEmuSize.Item2 <= 0)
                   return Tuple.Create((long)((double)originalImagePixelSize.Width / originalImageDpi.Width * 914400.0),
                                      (long)((double)originalImagePixelSize.Height / originalImageDpi.Height * 914400.0)); // 回退到自然大小
                // 如果原始图像参数不好，则回退到小尺寸
                return Tuple.Create(100000L, 100000L); // 回退到小尺寸
            }

            // 计算原始图像的"自然"EMU尺寸
            double naturalWidthEmu = ((double)originalImagePixelSize.Width / originalImageDpi.Width) * 914400.0;
            double naturalHeightEmu = ((double)originalImagePixelSize.Height / originalImageDpi.Height) * 914400.0;

            if (naturalWidthEmu <= 0 || naturalHeightEmu <= 0)
            {
                 return Tuple.Create(targetPlaceholderEmuSize.Item1, targetPlaceholderEmuSize.Item2); // 如果自然尺寸为零，则回退到目标尺寸
            }

            // 计算缩放比例以适应目标占位符
            double widthRatio = (double)targetPlaceholderEmuSize.Item1 / naturalWidthEmu;
            double heightRatio = (double)targetPlaceholderEmuSize.Item2 / naturalHeightEmu;

            // 使用较小的比例，确保图像按比例适应
            double scalingFactor = Math.Min(widthRatio, heightRatio);

            long finalWidthEmu = (long)(naturalWidthEmu * scalingFactor);
            long finalHeightEmu = (long)(naturalHeightEmu * scalingFactor);
            
            // 如果缩放结果太小，确保最小尺寸
            if (finalWidthEmu < 12700) finalWidthEmu = 12700; // 1点
            if (finalHeightEmu < 12700) finalHeightEmu = 12700;

            return Tuple.Create(finalWidthEmu, finalHeightEmu);
        }

        /// <summary>
        /// 根据文件扩展名获取NPOI的图片类型
        /// </summary>
        private int GetPictureTypeForNPOI(string extension)
        {
            switch (extension.ToLower())
            {
                case ".emf": return (int)PictureType.EMF;
                case ".wmf": return (int)PictureType.WMF;
                case ".pict": return (int)PictureType.PICT;
                case ".jpeg":
                case ".jpg": return (int)PictureType.JPEG;
                case ".png": return (int)PictureType.PNG;
                case ".dib": return (int)PictureType.DIB;
                case ".gif": // NPOI通常将GIF转换为PNG进行插入
                    return (int)PictureType.PNG; 
                case ".tiff": return (int)PictureType.TIFF;
                case ".eps": return (int)PictureType.EPS;
                case ".bmp": return (int)PictureType.BMP;
                case ".wpg": return (int)PictureType.WPG;
                default:
                    // 未知或不支持的扩展名，默认使用PNG
                    return (int)PictureType.PNG;
            }
        }

        /// <summary>
        /// 查找包含"打印图"的DWG文件
        /// </summary>
        /// <param name="directoryPath">目录路径</param>
        /// <returns>DWG文件路径数组</returns>
        private string[] FindDwgFiles(string directoryPath)
        {
            try
            {
                var dwgFiles = Directory.GetFiles(directoryPath, "*.dwg", SearchOption.AllDirectories)
                    .Where(file => Path.GetFileName(file).Contains("打印图"))
                    .ToArray();

                return dwgFiles;
            }
            catch (Exception)
            {
                return new string[0];
            }
        }

        /// <summary>
        /// 处理DWG文件转换为图片
        /// </summary>
        /// <param name="dwgFiles">DWG文件路径数组</param>
        /// <param name="printImageFolder">打印图输出文件夹路径</param>
        /// <param name="progressCallback">进度回调</param>
        /// <returns>处理结果</returns>
        private async Task<bool> ProcessDwgFiles(string[] dwgFiles, string printImageFolder, Action<int, string> progressCallback)
        {
            try
            {
                progressCallback?.Invoke(18, "[INFO]开始DWG文件处理管道...");

                // 上传并处理每个DWG文件
                for (int i = 0; i < dwgFiles.Length; i++)
                {
                    string dwgFile = dwgFiles[i];
                    int baseProgress = 20 + (i * 25 / dwgFiles.Length);
                    progressCallback?.Invoke(baseProgress, $"[INFO]生成平面图 {i + 1}/{dwgFiles.Length}: {Path.GetFileName(dwgFile)}");

                    // 上传DWG文件到API
                    string sessionId = await UploadDwgFileToApi(dwgFile, progressCallback);
                    if (string.IsNullOrEmpty(sessionId))
                    {
                        progressCallback?.Invoke(0, $"[ERROR]上传DWG文件失败: {Path.GetFileName(dwgFile)}");
                        return false;
                    }

                    // 等待平面图生成完成并下载结果（增强的进度监控）
                    string zipFilePath = await WaitForProcessingAndDownloadWithEnhancedMonitoring(sessionId, baseProgress, progressCallback);
                    if (string.IsNullOrEmpty(zipFilePath))
                    {
                        progressCallback?.Invoke(0, $"[ERROR]生成平面图失败: {Path.GetFileName(dwgFile)}");
                        return false;
                    }

                    // 解压到打印图文件夹
                    bool extractResult = ExtractZipToFolder(zipFilePath, printImageFolder, progressCallback);
                    if (!extractResult)
                    {
                        progressCallback?.Invoke(0, $"[ERROR]解压文件失败: {Path.GetFileName(zipFilePath)}");
                        return false;
                    }

                    // 清理临时ZIP文件
                    try
                    {
                        if (File.Exists(zipFilePath))
                        {
                            File.Delete(zipFilePath);
                        }
                    }
                    catch
                    {
                        // 忽略清理错误
                    }
                }

                progressCallback?.Invoke(45, "[INFO]所有DWG文件平面图生成完成");
                return true;
            }
            catch (Exception ex)
            {
                progressCallback?.Invoke(0, $"[ERROR]DWG文件处理失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 上传DWG文件到API
        /// </summary>
        /// <param name="dwgFilePath">DWG文件路径</param>
        /// <param name="progressCallback">进度回调</param>
        /// <returns>会话ID</returns>
        private async Task<string> UploadDwgFileToApi(string dwgFilePath, Action<int, string> progressCallback)
        {
            try
            {
                using (var httpClient = await HttpClientFactoryInstance.Instance.CreateClientAsync())
                {
                    // 使用配置服务获取API URL - 使用新的平面图生成端点
                    string apiUrl = _configService.GetDrawingProcessingApiUrl("generate-floor-plans");

                    progressCallback?.Invoke(22, $"[INFO]连接到服务器: {_configService.ServerAddress}");

                    using (var formData = new MultipartFormDataContent())
                    using (var fileStream = new FileStream(dwgFilePath, FileMode.Open, FileAccess.Read))
                    {
                        var fileContent = new StreamContent(fileStream);
                        fileContent.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/octet-stream");
                        formData.Add(fileContent, "file", Path.GetFileName(dwgFilePath));

                        progressCallback?.Invoke(25, $"[INFO]上传文件到平面图生成API: {Path.GetFileName(dwgFilePath)}");

                        var response = await httpClient.PostAsync(apiUrl, formData);
                        var responseContent = await response.Content.ReadAsStringAsync();

                        if (response.IsSuccessStatusCode)
                        {
                            var jsonDoc = JsonDocument.Parse(responseContent);
                            if (jsonDoc.RootElement.TryGetProperty("success", out var successElement) &&
                                successElement.GetBoolean() &&
                                jsonDoc.RootElement.TryGetProperty("sessionId", out var sessionIdElement))
                            {
                                string sessionId = sessionIdElement.GetString();
                                progressCallback?.Invoke(30, $"[INFO]文件上传成功，会话ID: {sessionId}");
                                return sessionId;
                            }
                        }

                        progressCallback?.Invoke(0, $"[ERROR]API响应错误: {responseContent}");
                        return null;
                    }
                }
            }
            catch (Exception ex)
            {
                progressCallback?.Invoke(0, $"[ERROR]上传文件失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 等待平面图生成完成并下载结果
        /// </summary>
        /// <param name="sessionId">会话ID</param>
        /// <param name="progressCallback">进度回调</param>
        /// <returns>下载的ZIP文件路径</returns>
        private async Task<string> WaitForProcessingAndDownload(string sessionId, Action<int, string> progressCallback)
        {
            try
            {
                using (var httpClient = await HttpClientFactoryInstance.Instance.CreateLongRunningClientAsync())
                {

                    // 轮询处理状态
                    string statusUrl = _configService.GetDrawingProcessingApiUrl("status", sessionId);
                    int maxAttempts = 60; // 最多等待5分钟（每5秒检查一次）

                    for (int attempt = 0; attempt < maxAttempts; attempt++)
                    {
                        try
                        {
                            var statusResponse = await httpClient.GetAsync(statusUrl);
                            var statusContent = await statusResponse.Content.ReadAsStringAsync();

                            if (statusResponse.IsSuccessStatusCode)
                            {
                                var statusDoc = JsonDocument.Parse(statusContent);
                                var isCompleted = statusDoc.RootElement.GetProperty("isCompleted").GetBoolean();
                                var currentOperation = statusDoc.RootElement.GetProperty("currentOperation").GetString();

                                progressCallback?.Invoke(30 + (attempt * 10 / maxAttempts), $"[INFO]平面图生成状态: {currentOperation}");

                                if (isCompleted)
                                {
                                    if (currentOperation.Contains("成功完成"))
                                    {
                                        // 平面图生成完成，下载结果
                                        return await DownloadProcessingResult(sessionId, progressCallback);
                                    }
                                    else
                                    {
                                        progressCallback?.Invoke(0, $"[ERROR]平面图生成失败: {currentOperation}");
                                        return null;
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            progressCallback?.Invoke(30, $"[WARNING]状态检查失败: {ex.Message}");
                        }

                        // 等待5秒后重试
                        await Task.Delay(5000);
                    }

                    progressCallback?.Invoke(0, "[ERROR]处理超时");
                    return null;
                }
            }
            catch (Exception ex)
            {
                progressCallback?.Invoke(0, $"[ERROR]等待处理失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 等待平面图生成完成并下载结果（增强的进度监控）
        /// </summary>
        /// <param name="sessionId">会话ID</param>
        /// <param name="baseProgress">基础进度值</param>
        /// <param name="progressCallback">进度回调</param>
        /// <returns>下载的ZIP文件路径</returns>
        private async Task<string> WaitForProcessingAndDownloadWithEnhancedMonitoring(string sessionId, int baseProgress, Action<int, string> progressCallback)
        {
            try
            {
                using (var httpClient = new HttpClient())
                {
                    httpClient.Timeout = TimeSpan.FromMinutes(_configService.ConnectionTimeoutMinutes);

                    // 轮询处理状态 - 每2秒检查一次
                    string statusUrl = _configService.GetDrawingProcessingApiUrl("status", sessionId);
                    string filesUrl = _configService.GetDrawingProcessingApiUrl("files", sessionId);
                    int maxAttempts = 150; // 最多等待5分钟（每2秒检查一次）

                    int lastDwgCount = 0;
                    int lastGifCount = 0;

                    for (int attempt = 0; attempt < maxAttempts; attempt++)
                    {
                        try
                        {
                            // 获取状态信息
                            var statusResponse = await httpClient.GetAsync(statusUrl);
                            var statusContent = await statusResponse.Content.ReadAsStringAsync();

                            if (statusResponse.IsSuccessStatusCode)
                            {
                                var statusDoc = JsonDocument.Parse(statusContent);
                                var isCompleted = statusDoc.RootElement.GetProperty("isCompleted").GetBoolean();
                                var currentOperation = statusDoc.RootElement.GetProperty("currentOperation").GetString();
                                var fileCount = statusDoc.RootElement.GetProperty("fileCount").GetInt32();
                                var dwgFileCount = statusDoc.RootElement.GetProperty("dwgFileCount").GetInt32();
                                var gifFileCount = statusDoc.RootElement.GetProperty("gifFileCount").GetInt32();
                                var fileSize = statusDoc.RootElement.GetProperty("fileSize").GetInt64();

                                // 计算进度百分比
                                int progressPercent = baseProgress + (attempt * 15 / maxAttempts);

                                // 根据文件类型比例显示不同的状态消息
                                string detailedStatus = GetDetailedProcessingStatus(currentOperation, dwgFileCount, gifFileCount, lastDwgCount, lastGifCount);

                                progressCallback?.Invoke(progressPercent, $"[INFO]{detailedStatus} (DWG:{dwgFileCount}, GIF:{gifFileCount}, 总计:{fileCount}个文件, {FormatFileSize(fileSize)})");

                                // 更新文件计数
                                lastDwgCount = dwgFileCount;
                                lastGifCount = gifFileCount;

                                // 如果处理完成
                                if (isCompleted)
                                {
                                    if (currentOperation.Contains("成功完成"))
                                    {
                                        progressCallback?.Invoke(baseProgress + 18, $"[INFO]平面图生成完成，共生成{gifFileCount}个图片文件，开始下载...");
                                        return await DownloadProcessingResult(sessionId, progressCallback);
                                    }
                                    else
                                    {
                                        progressCallback?.Invoke(0, $"[ERROR]处理失败: {currentOperation}");
                                        return null;
                                    }
                                }

                                // 可选：获取详细文件列表（每10次检查一次以减少API调用）
                                if (attempt % 5 == 0)
                                {
                                    await LogDetailedFileStatus(httpClient, filesUrl, progressCallback);
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            progressCallback?.Invoke(baseProgress, $"[WARNING]状态检查失败: {ex.Message}");
                        }

                        // 等待2秒后重试
                        await Task.Delay(2000);
                    }

                    progressCallback?.Invoke(0, "[ERROR]处理超时");
                    return null;
                }
            }
            catch (Exception ex)
            {
                progressCallback?.Invoke(0, $"[ERROR]等待处理失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取详细的处理状态描述
        /// </summary>
        /// <param name="currentOperation">当前操作</param>
        /// <param name="dwgFileCount">DWG文件数量</param>
        /// <param name="gifFileCount">GIF文件数量</param>
        /// <param name="lastDwgCount">上次DWG文件数量</param>
        /// <param name="lastGifCount">上次GIF文件数量</param>
        /// <returns>详细状态描述</returns>
        private string GetDetailedProcessingStatus(string currentOperation, int dwgFileCount, int gifFileCount, int lastDwgCount, int lastGifCount)
        {
            // 检查文件数量变化
            bool dwgIncreased = dwgFileCount > lastDwgCount;
            bool gifIncreased = gifFileCount > lastGifCount;

            // 根据文件类型比例和变化情况确定状态
            if (currentOperation.Contains("CAD数据拆分中") || (dwgFileCount > 0 && gifFileCount == 0))
            {
                return dwgIncreased ? "CAD数据拆分中..." : "CAD数据拆分中...";
            }
            else if (currentOperation.Contains("图片转换中") || (dwgFileCount > 0 && gifFileCount > 0))
            {
                return gifIncreased ? "图片转换中..." : "图片转换中...";
            }
            else if (currentOperation.Contains("成功完成"))
            {
                return "处理完成";
            }
            else if (currentOperation.Contains("正在处理"))
            {
                return "正在处理中...";
            }
            else
            {
                return currentOperation ?? "处理中...";
            }
        }

        /// <summary>
        /// 格式化文件大小
        /// </summary>
        /// <param name="bytes">字节数</param>
        /// <returns>格式化的文件大小</returns>
        private string FormatFileSize(long bytes)
        {
            if (bytes == 0) return "0 B";

            string[] sizes = { "B", "KB", "MB", "GB" };
            int order = 0;
            double size = bytes;

            while (size >= 1024 && order < sizes.Length - 1)
            {
                order++;
                size = size / 1024;
            }

            return $"{size:0.##} {sizes[order]}";
        }

        /// <summary>
        /// 记录详细的文件状态
        /// </summary>
        /// <param name="httpClient">HTTP客户端</param>
        /// <param name="filesUrl">文件列表API URL</param>
        /// <param name="progressCallback">进度回调</param>
        private async Task LogDetailedFileStatus(HttpClient httpClient, string filesUrl, Action<int, string> progressCallback)
        {
            try
            {
                var filesResponse = await httpClient.GetAsync(filesUrl);
                if (filesResponse.IsSuccessStatusCode)
                {
                    var filesContent = await filesResponse.Content.ReadAsStringAsync();
                    var filesDoc = JsonDocument.Parse(filesContent);

                    if (filesDoc.RootElement.ValueKind == JsonValueKind.Array)
                    {
                        var files = filesDoc.RootElement.EnumerateArray().ToList();
                        if (files.Count > 0)
                        {
                            // 显示最近的3个文件
                            int startIndex = Math.Max(0, files.Count - 3);
                            for (int i = startIndex; i < files.Count; i++)
                            {
                                var file = files[i];
                                if (file.TryGetProperty("fileName", out var fileNameProp))
                                {
                                    string fileName = fileNameProp.GetString();
                                    progressCallback?.Invoke(-1, $"[DEBUG]最新文件: {fileName}"); // -1 表示不更新进度条
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // 忽略文件列表获取错误，不影响主流程
                progressCallback?.Invoke(-1, $"[DEBUG]获取文件列表失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 下载平面图生成结果
        /// </summary>
        /// <param name="sessionId">会话ID</param>
        /// <param name="progressCallback">进度回调</param>
        /// <returns>下载的ZIP文件路径</returns>
        private async Task<string> DownloadProcessingResult(string sessionId, Action<int, string> progressCallback)
        {
            try
            {
                using (var httpClient = await HttpClientFactoryInstance.Instance.CreateClientAsync())
                {

                    string downloadUrl = _configService.GetDrawingProcessingApiUrl("download", sessionId);
                    progressCallback?.Invoke(35, "[INFO]开始下载平面图生成结果...");

                    var response = await httpClient.GetAsync(downloadUrl);

                    if (response.IsSuccessStatusCode)
                    {
                        // 创建临时文件保存下载的ZIP
                        string tempZipPath = Path.Combine(Path.GetTempPath(), $"dwg_result_{sessionId}.zip");

                        using (var fileStream = new FileStream(tempZipPath, FileMode.Create))
                        {
                            await response.Content.CopyToAsync(fileStream);
                        }

                        progressCallback?.Invoke(40, $"[INFO]下载完成: {tempZipPath}");
                        return tempZipPath;
                    }
                    else
                    {
                        var errorContent = await response.Content.ReadAsStringAsync();
                        progressCallback?.Invoke(0, $"[ERROR]下载失败: {errorContent}");
                        return null;
                    }
                }
            }
            catch (Exception ex)
            {
                progressCallback?.Invoke(0, $"[ERROR]下载失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 解压ZIP文件到指定文件夹
        /// </summary>
        /// <param name="zipFilePath">ZIP文件路径</param>
        /// <param name="extractToFolder">解压目标文件夹</param>
        /// <param name="progressCallback">进度回调</param>
        /// <returns>解压结果</returns>
        private bool ExtractZipToFolder(string zipFilePath, string extractToFolder, Action<int, string> progressCallback)
        {
            try
            {
                progressCallback?.Invoke(42, $"[INFO]解压文件到: {extractToFolder}");

                // 确保目标文件夹存在
                Directory.CreateDirectory(extractToFolder);

                // 解压ZIP文件
                using (var fileStream = new FileStream(zipFilePath, FileMode.Open, FileAccess.Read))
                using (var zipStream = new ZipInputStream(fileStream))
                {
                    ZipEntry entry;
                    while ((entry = zipStream.GetNextEntry()) != null)
                    {
                        if (!entry.IsFile) continue;

                        string entryPath = Path.Combine(extractToFolder, entry.Name);
                        string entryDir = Path.GetDirectoryName(entryPath);
                        if (!Directory.Exists(entryDir))
                        {
                            Directory.CreateDirectory(entryDir);
                        }

                        using (var outputStream = File.Create(entryPath))
                        {
                            zipStream.CopyTo(outputStream);
                        }
                    }
                }

                progressCallback?.Invoke(45, "[INFO]文件解压完成");
                return true;
            }
            catch (Exception ex)
            {
                progressCallback?.Invoke(0, $"[ERROR]解压文件失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 查找打印图文件
        /// </summary>
        /// <param name="dataDirectoryPath">数据目录路径</param>
        /// <param name="outputDirectoryPath">输出目录路径（可选）</param>
        /// <returns>文件路径数组</returns>
        private string[] FindPrintImageFiles(string dataDirectoryPath, string outputDirectoryPath = null)
        {
            // 首先检查输出目录中的打印图文件夹（DWG处理后的结果）
            if (!string.IsNullOrEmpty(outputDirectoryPath))
            {
                string outputPrintImageFolder = Path.Combine(outputDirectoryPath, "打印图");
                if (Directory.Exists(outputPrintImageFolder))
                {
                    var outputFiles = Directory.GetFiles(outputPrintImageFolder, "*.*", SearchOption.AllDirectories)
                        .Where(file => IsImageFile(file))
                        .ToList();

                    if (outputFiles.Count > 0)
                    {
                        // 使用Windows文件名自然排序（处理数字部分）
                        outputFiles.Sort(new WindowsFileNameComparer());
                        return outputFiles.ToArray();
                    }
                }
            }

            // 查找数据目录中的打印图文件夹
            string printImageFolder = Path.Combine(dataDirectoryPath, "打印图");

            // 如果打印图文件夹不存在，尝试在子文件夹中查找
            if (!Directory.Exists(printImageFolder))
            {
                var subDirectories = Directory.GetDirectories(dataDirectoryPath);
                foreach (var subDir in subDirectories)
                {
                    string subPrintImageFolder = Path.Combine(subDir, "打印图");
                    if (Directory.Exists(subPrintImageFolder))
                    {
                        printImageFolder = subPrintImageFolder;
                        break;
                    }
                }
            }

            // 如果找到了打印图文件夹，查找图片文件
            if (Directory.Exists(printImageFolder))
            {
                // 获取所有常见图片文件类型
                var files = Directory.GetFiles(printImageFolder, "*.*", SearchOption.AllDirectories)
                    .Where(file => IsImageFile(file))
                    .ToList();

                // 使用Windows文件名自然排序（处理数字部分）
                files.Sort(new WindowsFileNameComparer());
                return files.ToArray();
            }

            return new string[0]; // 返回空数组表示未找到文件
        }

        /// <summary>
        /// Windows风格的文件名自然排序比较器
        /// </summary>
        private class WindowsFileNameComparer : IComparer<string>
        {
            public int Compare(string x, string y)
            {
                // 如果路径相同，直接返回0
                if (x == y) return 0;
                
                // 只比较文件名部分（不包括路径）
                string fileNameX = Path.GetFileName(x);
                string fileNameY = Path.GetFileName(y);

                // Windows式自然排序
                return NaturalCompare(fileNameX, fileNameY);
            }
            
            /// <summary>
            /// 自然排序比较方法，按照Windows文件资源管理器的排序方式
            /// </summary>
            private int NaturalCompare(string x, string y)
            {
                // 分割字符串为数字和非数字部分的正则表达式
                var regex = new Regex(@"(\d+)|(\D+)");
                
                // 获取匹配项
                var xMatches = regex.Matches(x);
                var yMatches = regex.Matches(y);
                
                // 逐一比较各个部分
                for (int i = 0; i < Math.Min(xMatches.Count, yMatches.Count); i++)
                {
                    string xPart = xMatches[i].Value;
                    string yPart = yMatches[i].Value;
                    
                    // 检查这部分是否是数字
                    bool xIsNumber = char.IsDigit(xPart[0]);
                    bool yIsNumber = char.IsDigit(yPart[0]);
                    
                    int result;
                    
                    // 如果两部分都是数字
                    if (xIsNumber && yIsNumber)
                    {
                        // 将字符串转换为数字进行比较
                        int xNum = int.Parse(xPart);
                        int yNum = int.Parse(yPart);
                        result = xNum.CompareTo(yNum);
                    }
                    else // 如果有一个不是数字，或者两个都不是数字
                    {
                        // 按字符串大小写不敏感比较
                        result = string.Compare(xPart, yPart, StringComparison.OrdinalIgnoreCase);
                    }
                    
                    // 如果这一部分不相等，返回比较结果
                    if (result != 0)
                    {
                        return result;
                    }
                }
                
                // 如果所有匹配的部分都相同，但长度不同
                return xMatches.Count.CompareTo(yMatches.Count);
            }
        }

        /// <summary>
        /// 判断文件是否为图片文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否为图片文件</returns>
        private bool IsImageFile(string filePath)
        {
            // 常见图片文件扩展名
            string[] imageExtensions = { ".jpg", ".jpeg", ".png", ".bmp", ".gif", ".tif", ".tiff", ".wmf", ".dxf" };
            string extension = Path.GetExtension(filePath).ToLower();
            
            return imageExtensions.Contains(extension);
        }

        /// <summary>
        /// 是否可用
        /// </summary>
        /// <param name="parameters">检查参数</param>
        /// <returns>是否可用</returns>
        public bool IsAvailable(Dictionary<string, object> parameters)
        {
            if (parameters == null)
                return false;

            // 检查必要参数
            if (!parameters.TryGetValue("DataFolder", out object dataDirectoryObj) || 
                !parameters.TryGetValue("OutputDir", out object outputDirectoryObj))
                return false;

            string dataDirectory = dataDirectoryObj?.ToString();
            
            // 检查数据目录
            if (string.IsNullOrEmpty(dataDirectory) || !Directory.Exists(dataDirectory))
                return false;
            
            // 检查模板文件
            string templatePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "报告模板", "13_房产分层测绘图", "房产分层测绘图.docx");
            if (!File.Exists(templatePath))
                return false;
            
            return true;
        }
    }
} 